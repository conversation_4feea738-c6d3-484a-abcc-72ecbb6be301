import 'dart:convert';
import 'dart:io';

import 'package:nb_utils/nb_utils.dart' as nb_utils;
import 'package:soho_souk/ApiUtils.dart';
import 'package:soho_souk/Classified_App/classified_app_radio_button.dart';
import 'package:soho_souk/main.dart';
import 'package:soho_souk/models/PostModel.dart';
import 'package:soho_souk/models/cityModel.dart';
import 'package:http/http.dart' as http;
import 'package:soho_souk/service/serviceApi.dart';
import 'package:soho_souk/widget/loader.dart';
import '../../Classified_App/classified_app_drop_down.dart';
import '../../Classified_App/classified_app_theme.dart';
import '../../Classified_App/classified_app_util.dart';
import '../../Classified_App/form_field_controller.dart';
import '../../Classified_App/upload_data.dart';
import '/pages/app_button/app_button_widget.dart';
import 'dart:async';
import '/custom_code/actions/index.dart' as actions;
import 'package:flutter/material.dart';
// import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'add_product_tab_model.dart';
export 'add_product_tab_model.dart';

class AddProductTabWidget extends StatefulWidget {
  const AddProductTabWidget({super.key});

  @override
  State<AddProductTabWidget> createState() => _AddProductTabWidgetState();
}

class _AddProductTabWidgetState extends State<AddProductTabWidget> {
  late AddProductTabModel _model;

  StreamSubscription<bool>? _keyboardVisibilitySubscription;
  bool _isKeyboardVisible = false;

  @override
  void setState(VoidCallback callback) {
    super.setState(callback);
    _model.onUpdate();
  }

  @override
  void initState() {
    super.initState();
    _model = createModel(context, () => AddProductTabModel());

    // if (!isWeb) {
    //   // _keyboardVisibilitySubscription =
    //   //     KeyboardVisibilityController().onChange.listen((bool visible) {
    //   //   setState(() {
    //   //     _isKeyboardVisible = visible;
    //   //   });
    //   // });
    // }

    _model.textController1 ??= TextEditingController();
    _model.textFieldFocusNode1 ??= FocusNode();

    _model.textController2 ??= TextEditingController();
    _model.textFieldFocusNode2 ??= FocusNode();

    _model.textController3 ??= TextEditingController();
    _model.textFieldFocusNode3 ??= FocusNode();

    _model.textController4 ??= TextEditingController();
    _model.textFieldFocusNode4 ??= FocusNode();

    _model.textController5 ??= TextEditingController();
    _model.textFieldFocusNode5 ??= FocusNode();

    _model.textController6 ??= TextEditingController();
    _model.textFieldFocusNode6 ??= FocusNode();

    _model.textController7 ??= TextEditingController();
    _model.textFieldFocusNode7 ??= FocusNode();

    // Initialize radio button controllers with default values
    _model.radioButtonValueController1 ??= FormFieldController<String>("Yes");
    _model.radioButtonValueController2 ??= FormFieldController<String>("Yes");
    _model.radioButtonValueController3 ??= FormFieldController<String>("Yes");

    _model.textController8 ??= TextEditingController();
    _model.textFieldFocusNode8 ??= FocusNode();
  }

  @override
  void dispose() {
    _model.maybeDispose();

    // if (!nb_utils.isWeb) {
    //   _keyboardVisibilitySubscription.cancel();
    // }
    super.dispose();
  }

  List<CityModels> childCity = [];
  int? city;
  int? area;
  String? barter;
  int primaryImage = 0;
  getArea(val) {
    List<CityModels> selectCity =
        appStore.mainCity.where((i) => i.city == val).toList();
    setState(() {
      city = selectCity[0].id;
      childCity = [];
      childCity = appStore.city.where((i) => i.parent_id == city).toList();
    });
  }

  Future<void> submitPost() async {
    try {
      showLoadingDialog(context);
      // Set up the URL
      var url = Uri.parse(
          '${ApiUtils.BASE_URL}save-postad'); // Replace with your Laravel API URL
      if (_model.uploadedLocalFiles.isEmpty ||
          _model.uploadedLocalFiles[0].path == null) {
        nb_utils.toast("No file selected or file path is null!",
            bgColor: Colors.red, textColor: Colors.black);
        Navigator.of(context, rootNavigator: true).pop(false);
        print('No file selected or file path is null');
        return;
      }
      // Create a multipart request
      var request = http.MultipartRequest('POST', url);
      // Loop through all files in `_model.uploadedLocalFiles`
      for (var fileData in _model.uploadedLocalFiles) {
        final filePath = fileData.path;

        if (filePath == null || !File(filePath).existsSync()) {
          print('File does not exist or path is invalid: $filePath');
          continue;
        }

        // Add each file to the request
        request.files.add(await http.MultipartFile.fromPath(
          'images[]', // Use 'images[]' as the key for multiple files (update key based on backend requirement)
          filePath,
        ));
      }

      request.fields['customer_id'] = appStore.user_id.toString();
      request.fields['post_type'] =
          _model.dropDownValue1 == "Normal Ad" ? "0" : "1";
      request.fields['category'] = FFAppState().selectedCategoryId.toString();
      request.fields['subcategory'] =
          FFAppState().selectedChildCategoryId.toString();
      request.fields['childcategory'] = FFAppState().haveSecondCat
          ? FFAppState().selectedSecondChildCategoryId.toString()
          : '0';
      request.fields['title'] =
          _model.textController1.text; // Replace with your input
      request.fields['price'] =
          _model.textController2.text; // Replace with your input
      request.fields['description'] =
          _model.textController4.text; // Replace with your input
      request.fields['city'] = city.toString();
      request.fields['area'] = area.toString();
      request.fields['city_name'] = _model.dropDownValue2.toString();
      request.fields['area_name'] = _model.dropDownValue3.toString();
      request.fields['vendor_name'] = appStore.user_name.toString();
      request.fields['primary_image'] = primaryImage.toString();
      request.fields['item_conditions'] =
          _model.dropDownValue4.toString(); // Example dropdown value
      request.fields['barter_enable'] = barter == "Yes" ? "1" : "0";
      request.fields['show_mobile'] =
          _model.radioButtonValueController2?.value == "Yes" ? "0" : "1";
      request.fields['enable_sms'] =
          _model.radioButtonValueController3?.value == "Yes" ? "0" : "1";

      print('Request fields: ${request.fields}');
      print('Request files: ${request.files}');

      // Send the request
      var response = await request.send();

      if (response.statusCode == 200) {
        Navigator.of(context, rootNavigator: true).pop(false);
        var responseData = await response.stream.bytesToString();
        debugPrint("Response: ${responseData}");
        nb_utils.toast("Post create successfully!",
            bgColor: Colors.green, textColor: Colors.black);
        //appStore.setNewPost(PostModel.fromJson(jsonDecode(responseData)));
        appStore.setAllPost(await ServiceApi().getAllPost());
        setState(() {
          FFAppState().selectedPageIndex = 0;
        });
        await appStore.pageViewController?.animateToPage(
          FFAppState().selectedPageIndex,
          duration: Duration(milliseconds: 500),
          curve: Curves.ease,
        );
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ad posted successfully!')),
        );
      } else {
        Navigator.of(context, rootNavigator: true).pop(false);
        print("Failed with status: ${response.statusCode}");
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to post ad!')),
        );
      }
    } catch (e) {
      print("Error: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('An error occurred!')),
      );
    }
  }

  void handlePostTypeSelection(String type) {
    if (type == 'Feature Ad / Paid Ad') {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text(
              'Coming Soon',
              style: ClassifiedAppTheme.of(context)
                  .titleMedium
                  .copyWith(color: Colors.red),
            ),
            content: Text(
              'This feature will be available soon!',
              style: ClassifiedAppTheme.of(context).bodyMedium,
            ),
            actions: [
              TextButton(
                child: Text(
                  'OK',
                  style: TextStyle(
                    color: ClassifiedAppTheme.of(context).primary,
                  ),
                ),
                onPressed: () {
                  setState(() {
                    _model.dropDownValue1 = "Normal Ad";
                    _model.dropDownValueController1 =
                        FormFieldController<String>("Normal Ad");
                  });
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    } else {
      // Handle other post type selections
      setState(() {
        _model.dropDownValueController1?.value = "Normal Ad";
        _model.dropDownValue1 = "Normal Ad";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    context.watch<FFAppState>();

    return appStore.logined
        ? Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with Progress Indicator
              Container(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 24.0, 16.0, 0.0),
                child: Column(
                  children: [
                    Text(
                      FFLocalizations.of(context).getText(
                        'ih5xf3ss' /* Add product */,
                      ),
                      style: ClassifiedAppTheme.of(context).bodyMedium.override(
                            fontFamily: 'Satoshi',
                            fontSize: 24.0,
                            fontWeight: FontWeight.bold,
                            useGoogleFonts: false,
                          ),
                    ),
                    SizedBox(height: 16.0),
                    // Progress Indicator
                    Container(
                      padding: EdgeInsetsDirectional.fromSTEB(
                          20.0, 16.0, 20.0, 16.0),
                      decoration: BoxDecoration(
                        color:
                            ClassifiedAppTheme.of(context).secondaryBackground,
                        borderRadius: BorderRadius.circular(12.0),
                        border: Border.all(
                          color: ClassifiedAppTheme.of(context)
                              .info
                              .withValues(alpha: 0.3),
                          width: 1.0,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Complete your listing',
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              Container(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    8.0, 4.0, 8.0, 4.0),
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .primary
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(6.0),
                                ),
                                child: Text(
                                  '4 steps',
                                  style: ClassifiedAppTheme.of(context)
                                      .bodySmall
                                      .override(
                                        fontFamily: 'Satoshi',
                                        fontSize: 12.0,
                                        color: ClassifiedAppTheme.of(context)
                                            .primary,
                                        fontWeight: FontWeight.w600,
                                        useGoogleFonts: false,
                                      ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12.0),
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  height: 4.0,
                                  decoration: BoxDecoration(
                                    color:
                                        ClassifiedAppTheme.of(context).primary,
                                    borderRadius: BorderRadius.circular(2.0),
                                  ),
                                ),
                              ),
                              SizedBox(width: 4.0),
                              Expanded(
                                child: Container(
                                  height: 4.0,
                                  decoration: BoxDecoration(
                                    color: ClassifiedAppTheme.of(context)
                                        .info
                                        .withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(2.0),
                                  ),
                                ),
                              ),
                              SizedBox(width: 4.0),
                              Expanded(
                                child: Container(
                                  height: 4.0,
                                  decoration: BoxDecoration(
                                    color: ClassifiedAppTheme.of(context)
                                        .info
                                        .withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(2.0),
                                  ),
                                ),
                              ),
                              SizedBox(width: 4.0),
                              Expanded(
                                child: Container(
                                  height: 4.0,
                                  decoration: BoxDecoration(
                                    color: ClassifiedAppTheme.of(context)
                                        .info
                                        .withValues(alpha: 0.3),
                                    borderRadius: BorderRadius.circular(2.0),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16.0),
                  ],
                ),
              ),
              // Enhanced Image Upload Section Header
              Container(
                margin: EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                padding: EdgeInsetsDirectional.fromSTEB(20.0, 16.0, 20.0, 16.0),
                decoration: BoxDecoration(
                  color: ClassifiedAppTheme.of(context).secondaryBackground,
                  borderRadius: BorderRadius.circular(16.0),
                  border: Border.all(
                    color: ClassifiedAppTheme.of(context)
                        .info
                        .withValues(alpha: 0.3),
                    width: 1.0,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10.0,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(8.0),
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .primary
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: Icon(
                            Icons.photo_library_outlined,
                            color: ClassifiedAppTheme.of(context).primary,
                            size: 20.0,
                          ),
                        ),
                        SizedBox(width: 12.0),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                FFLocalizations.of(context).getText(
                                  'ajd6i3t3' /* Upload property images */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 17.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 4.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  'i8qhw5bg' /* (max 10 photos) */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodySmall
                                    .override(
                                      fontFamily: 'Satoshi',
                                      color: ClassifiedAppTheme.of(context)
                                          .secondaryText,
                                      fontSize: 14.0,
                                      fontWeight: FontWeight.w400,
                                      useGoogleFonts: false,
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(16.0, 16.0, 0.0, 0.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Align(
                      alignment: AlignmentDirectional(-1.0, 0.0),
                      child: InkWell(
                        splashColor: Colors.transparent,
                        focusColor: Colors.transparent,
                        hoverColor: Colors.transparent,
                        highlightColor: Colors.transparent,
                        onTap: () async {
                          final selectedMedia = await selectMedia(
                            imageQuality: 100,
                            mediaSource: MediaSource.photoGallery,
                            multiImage: true,
                          );
                          if (selectedMedia != null &&
                              selectedMedia.every((m) =>
                                  validateFileFormat(m.storagePath, context))) {
                            setState(() => _model.isDataUploading = true);
                            var selectedUploadedFiles = <FFUploadedFile>[];

                            try {
                              showUploadMessage(
                                context,
                                'Uploading file...',
                                showLoading: true,
                              );
                              selectedUploadedFiles = selectedMedia
                                  .map((m) => FFUploadedFile(
                                      name: m.storagePath.split('/').last,
                                      bytes: m.bytes,
                                      height: m.dimensions?.height,
                                      width: m.dimensions?.width,
                                      blurHash: m.blurHash,
                                      path: m.filePath))
                                  .toList();
                            } finally {
                              ScaffoldMessenger.of(context)
                                  .hideCurrentSnackBar();
                              _model.isDataUploading = false;
                            }
                            if (selectedUploadedFiles.length ==
                                selectedMedia.length) {
                              setState(() {
                                _model.uploadedLocalFiles =
                                    selectedUploadedFiles;
                              });
                              showUploadMessage(context, 'Success!');
                            } else {
                              setState(() {});
                              showUploadMessage(
                                  context, 'Failed to upload data');
                              return;
                            }
                          }
                        },
                        child: Container(
                          width: 100.0,
                          height: 100.0,
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .primary
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                              color: ClassifiedAppTheme.of(context)
                                  .primary
                                  .withValues(alpha: 0.3),
                              width: 2.0,
                              style: BorderStyle.solid,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: EdgeInsets.all(8.0),
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context).primary,
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                                child: Icon(
                                  Icons.add_photo_alternate_outlined,
                                  color: Colors.white,
                                  size: 24.0,
                                ),
                              ),
                              SizedBox(height: 8.0),
                              Text(
                                'Add Photos',
                                style: ClassifiedAppTheme.of(context)
                                    .bodySmall
                                    .override(
                                      fontFamily: 'Satoshi',
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      fontSize: 12.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                                textAlign: TextAlign.center,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsetsDirectional.fromSTEB(
                            16.0, 0.0, 0.0, 20.0),
                        child: Container(
                          height: 90.0,
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                          ),
                          child: Visibility(
                            visible: _model.uploadedLocalFiles.isNotEmpty,
                            child: Builder(
                              builder: (context) {
                                List<FFUploadedFile> uplodeImageList = _model
                                    .uploadedLocalFiles
                                    .map((e) => e)
                                    .toList();
                                return ListView.separated(
                                  padding: EdgeInsets.fromLTRB(
                                    0,
                                    0,
                                    16.0,
                                    0,
                                  ),
                                  scrollDirection: Axis.horizontal,
                                  itemCount: uplodeImageList.length,
                                  separatorBuilder: (_, __) =>
                                      SizedBox(width: 16.0),
                                  itemBuilder: (context, uplodeImageListIndex) {
                                    final uplodeImageListItem =
                                        uplodeImageList[uplodeImageListIndex];
                                    return Container(
                                      margin: EdgeInsets.only(right: 8.0),
                                      child: Stack(
                                        alignment:
                                            AlignmentDirectional(1.0, -1.0),
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              setState(() {
                                                primaryImage =
                                                    uplodeImageListIndex;
                                              });
                                            },
                                            child: Container(
                                              width: 100.0,
                                              height: 100.0,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(16.0),
                                                border: Border.all(
                                                  color: primaryImage ==
                                                          uplodeImageListIndex
                                                      ? ClassifiedAppTheme.of(
                                                              context)
                                                          .primary
                                                      : ClassifiedAppTheme.of(
                                                              context)
                                                          .info
                                                          .withValues(
                                                              alpha: 0.3),
                                                  width: primaryImage ==
                                                          uplodeImageListIndex
                                                      ? 3.0
                                                      : 1.0,
                                                ),
                                                boxShadow: [
                                                  BoxShadow(
                                                    color: Colors.black
                                                        .withValues(alpha: 0.1),
                                                    blurRadius: 8.0,
                                                    offset: Offset(0, 2),
                                                  ),
                                                ],
                                              ),
                                              child: Stack(
                                                children: [
                                                  ClipRRect(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            15.0),
                                                    child: Image.memory(
                                                      uplodeImageListItem
                                                              .bytes ??
                                                          Uint8List.fromList(
                                                              []),
                                                      width: 100.0,
                                                      height: 100.0,
                                                      fit: BoxFit.cover,
                                                    ),
                                                  ),
                                                  if (primaryImage ==
                                                      uplodeImageListIndex)
                                                    Container(
                                                      width: 100.0,
                                                      height: 100.0,
                                                      decoration: BoxDecoration(
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(15.0),
                                                        color: Colors.black
                                                            .withValues(
                                                                alpha: 0.3),
                                                      ),
                                                      child: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          Container(
                                                            padding:
                                                                EdgeInsets.all(
                                                                    6.0),
                                                            decoration:
                                                                BoxDecoration(
                                                              color: ClassifiedAppTheme
                                                                      .of(context)
                                                                  .primary,
                                                              borderRadius:
                                                                  BorderRadius
                                                                      .circular(
                                                                          6.0),
                                                            ),
                                                            child: Icon(
                                                              Icons.star,
                                                              color:
                                                                  Colors.white,
                                                              size: 16.0,
                                                            ),
                                                          ),
                                                          SizedBox(height: 4.0),
                                                          Text(
                                                            "Primary",
                                                            style: TextStyle(
                                                              color:
                                                                  Colors.white,
                                                              fontSize: 10.0,
                                                              fontWeight:
                                                                  FontWeight
                                                                      .w600,
                                                              fontFamily:
                                                                  'Satoshi',
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                ],
                                              ),
                                            ),
                                          ),
                                          // Enhanced Delete Button
                                          Positioned(
                                            top: -5.0,
                                            right: -5.0,
                                            child: InkWell(
                                              splashColor: Colors.transparent,
                                              focusColor: Colors.transparent,
                                              hoverColor: Colors.transparent,
                                              highlightColor:
                                                  Colors.transparent,
                                              onTap: () {
                                                FFAppState().update(() async {
                                                  _model.uploadedLocalFiles =
                                                      await actions.removeAtI(
                                                    uplodeImageListIndex,
                                                    _model.uploadedLocalFiles
                                                        .toList(),
                                                  );
                                                  uplodeImageList = _model
                                                      .uploadedLocalFiles
                                                      .toList();
                                                });
                                              },
                                              child: Container(
                                                width: 28.0,
                                                height: 28.0,
                                                decoration: BoxDecoration(
                                                  color: Colors.red,
                                                  shape: BoxShape.circle,
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Colors.black
                                                          .withValues(
                                                              alpha: 0.2),
                                                      blurRadius: 4.0,
                                                      offset: Offset(0, 2),
                                                    ),
                                                  ],
                                                ),
                                                child: Icon(
                                                  Icons.close,
                                                  color: Colors.white,
                                                  size: 16.0,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    );
                                  },
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Form(
                  key: _model.formKey,
                  autovalidateMode: AutovalidateMode.disabled,
                  child: Padding(
                    padding:
                        EdgeInsetsDirectional.fromSTEB(16.0, 0.0, 16.0, 0.0),
                    child: ListView(
                      padding: EdgeInsets.fromLTRB(
                        0,
                        0,
                        0,
                        24.0,
                      ),
                      physics: AlwaysScrollableScrollPhysics(),
                      scrollDirection: Axis.vertical,
                      children: [
                        // Category Selection Section
                        Container(
                          margin: EdgeInsetsDirectional.fromSTEB(
                              0.0, 24.0, 0.0, 0.0),
                          padding: EdgeInsetsDirectional.fromSTEB(
                              20.0, 20.0, 20.0, 20.0),
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                              color: ClassifiedAppTheme.of(context)
                                  .info
                                  .withValues(alpha: 0.3),
                              width: 1.0,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 10.0,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(8.0),
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Icon(
                                      Icons.category_outlined,
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      size: 20.0,
                                    ),
                                  ),
                                  SizedBox(width: 12.0),
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      'prtc2xw5' /* Choose category */,
                                    ),
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 16.0),
                              InkWell(
                                splashColor: Colors.transparent,
                                focusColor: Colors.transparent,
                                hoverColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: () async {
                                  context.pushNamed(
                                    'ChooseCategoriesPage',
                                    extra: <String, dynamic>{
                                      kTransitionInfoKey: TransitionInfo(
                                        hasTransition: true,
                                        transitionType:
                                            PageTransitionType.rightToLeft,
                                        duration: Duration(milliseconds: 300),
                                      ),
                                    },
                                  );
                                },
                                child: Container(
                                  width: double.infinity,
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      16.0, 16.0, 16.0, 16.0),
                                  decoration: BoxDecoration(
                                    color: ClassifiedAppTheme.of(context)
                                        .primaryBackground,
                                    borderRadius: BorderRadius.circular(12.0),
                                    border: Border.all(
                                      color: ClassifiedAppTheme.of(context)
                                          .info
                                          .withValues(alpha: 0.5),
                                      width: 1.0,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          valueOrDefault<String>(
                                            FFAppState().selectedCategory,
                                            'Select product category',
                                          ),
                                          style: ClassifiedAppTheme.of(context)
                                              .bodyMedium
                                              .override(
                                                fontFamily: 'Satoshi',
                                                color: FFAppState()
                                                        .selectedCategory
                                                        .isNotEmpty
                                                    ? ClassifiedAppTheme.of(
                                                            context)
                                                        .primaryText
                                                    : ClassifiedAppTheme.of(
                                                            context)
                                                        .secondaryText,
                                                fontSize: 16.0,
                                                fontWeight: FontWeight.w500,
                                                useGoogleFonts: false,
                                              ),
                                        ),
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryText,
                                        size: 16.0,
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              if (FFAppState().selectedCategoryId != 0) ...[
                                SizedBox(height: 16.0),
                                InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    context.pushNamed(
                                      'ChooseSubCategoriesPage',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        16.0, 16.0, 16.0, 16.0),
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .primaryBackground,
                                      borderRadius: BorderRadius.circular(12.0),
                                      border: Border.all(
                                        color: ClassifiedAppTheme.of(context)
                                            .info
                                            .withValues(alpha: 0.5),
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            valueOrDefault<String>(
                                              FFAppState()
                                                  .selectedChildCategory,
                                              'Select product subcategory',
                                            ),
                                            style: ClassifiedAppTheme.of(
                                                    context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  color: FFAppState()
                                                          .selectedChildCategory
                                                          .isNotEmpty
                                                      ? ClassifiedAppTheme.of(
                                                              context)
                                                          .primaryText
                                                      : ClassifiedAppTheme.of(
                                                              context)
                                                          .secondaryText,
                                                  fontSize: 16.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          color: ClassifiedAppTheme.of(context)
                                              .secondaryText,
                                          size: 16.0,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                              if (FFAppState().haveSecondCat) ...[
                                SizedBox(height: 16.0),
                                InkWell(
                                  splashColor: Colors.transparent,
                                  focusColor: Colors.transparent,
                                  hoverColor: Colors.transparent,
                                  highlightColor: Colors.transparent,
                                  onTap: () async {
                                    context.pushNamed(
                                      'ChooseSecondSubCategoriesPage',
                                      extra: <String, dynamic>{
                                        kTransitionInfoKey: TransitionInfo(
                                          hasTransition: true,
                                          transitionType:
                                              PageTransitionType.rightToLeft,
                                          duration: Duration(milliseconds: 300),
                                        ),
                                      },
                                    );
                                  },
                                  child: Container(
                                    width: double.infinity,
                                    padding: EdgeInsetsDirectional.fromSTEB(
                                        16.0, 16.0, 16.0, 16.0),
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .primaryBackground,
                                      borderRadius: BorderRadius.circular(12.0),
                                      border: Border.all(
                                        color: ClassifiedAppTheme.of(context)
                                            .info
                                            .withValues(alpha: 0.5),
                                        width: 1.0,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      children: [
                                        Expanded(
                                          child: Text(
                                            valueOrDefault<String>(
                                              FFAppState()
                                                  .selectedSecondChildCategory,
                                              'Select product child category',
                                            ),
                                            style: ClassifiedAppTheme.of(
                                                    context)
                                                .bodyMedium
                                                .override(
                                                  fontFamily: 'Satoshi',
                                                  color: FFAppState()
                                                          .selectedSecondChildCategory
                                                          .isNotEmpty
                                                      ? ClassifiedAppTheme.of(
                                                              context)
                                                          .primaryText
                                                      : ClassifiedAppTheme.of(
                                                              context)
                                                          .secondaryText,
                                                  fontSize: 16.0,
                                                  fontWeight: FontWeight.w500,
                                                  useGoogleFonts: false,
                                                ),
                                          ),
                                        ),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          color: ClassifiedAppTheme.of(context)
                                              .secondaryText,
                                          size: 16.0,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        // Product Details Section
                        Container(
                          margin: EdgeInsetsDirectional.fromSTEB(
                              0.0, 24.0, 0.0, 0.0),
                          padding: EdgeInsetsDirectional.fromSTEB(
                              20.0, 20.0, 20.0, 20.0),
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                              color: ClassifiedAppTheme.of(context)
                                  .info
                                  .withValues(alpha: 0.3),
                              width: 1.0,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 10.0,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(8.0),
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Icon(
                                      Icons.inventory_2_outlined,
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      size: 20.0,
                                    ),
                                  ),
                                  SizedBox(width: 12.0),
                                  Text(
                                    'Product Details',
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  'h2jy6c4t' /* Product name */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 8.0),
                              TextFormField(
                                controller: _model.textController1,
                                focusNode: _model.textFieldFocusNode1,
                                textInputAction: TextInputAction.next,
                                obscureText: false,
                                decoration: InputDecoration(
                                  hintText: FFLocalizations.of(context).getText(
                                    '566r7knm' /* Enter product name */,
                                  ),
                                  hintStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryText,
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w400,
                                        useGoogleFonts: false,
                                      ),
                                  filled: true,
                                  fillColor: ClassifiedAppTheme.of(context)
                                      .primaryBackground,
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: ClassifiedAppTheme.of(context)
                                          .info
                                          .withValues(alpha: 0.5),
                                      width: 1.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      width: 2.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          ClassifiedAppTheme.of(context).error,
                                      width: 1.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          ClassifiedAppTheme.of(context).error,
                                      width: 2.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  contentPadding:
                                      EdgeInsetsDirectional.fromSTEB(
                                          16.0, 16.0, 16.0, 16.0),
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                keyboardType: TextInputType.name,
                                cursorColor:
                                    ClassifiedAppTheme.of(context).primary,
                                validator: _model.textController1Validator
                                    .asValidator(context),
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  'dza85s91' /* Type */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 8.0),
                              ClassifiedAppDropDown<String>(
                                controller: _model.dropDownValueController1 ??=
                                    FormFieldController<String>(null),
                                options: ["Normal Ad", "Feature Ad / Paid Ad"],
                                onChanged: (val) =>
                                    handlePostTypeSelection(val as String),
                                width: double.infinity,
                                height: 56.0,
                                textStyle: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                hintText: "Choose Post Type",
                                icon: Icon(
                                  Icons.keyboard_arrow_down_rounded,
                                  color: ClassifiedAppTheme.of(context)
                                      .secondaryText,
                                  size: 20.0,
                                ),
                                fillColor: ClassifiedAppTheme.of(context)
                                    .primaryBackground,
                                elevation: 0.0,
                                borderColor: ClassifiedAppTheme.of(context)
                                    .info
                                    .withValues(alpha: 0.5),
                                borderWidth: 1.0,
                                borderRadius: 12.0,
                                margin: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 4.0, 16.0, 4.0),
                                hidesUnderline: true,
                                isOverButton: true,
                                isSearchable: false,
                                isMultiSelect: false,
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  'k3bm4fpa' /* Item Conditions */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 8.0),
                              ClassifiedAppDropDown<String>(
                                controller: _model.dropDownValueController4 ??=
                                    FormFieldController<String>(null),
                                options: appStore.condition
                                    .map((e) => e.toString())
                                    .toList(),
                                onChanged: (val) =>
                                    setState(() => _model.dropDownValue4 = val),
                                width: double.infinity,
                                height: 56.0,
                                textStyle: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                hintText: FFLocalizations.of(context).getText(
                                  'unique_key_1' /* Item Conditions */,
                                ),
                                icon: Icon(
                                  Icons.keyboard_arrow_down_rounded,
                                  color: ClassifiedAppTheme.of(context)
                                      .secondaryText,
                                  size: 20.0,
                                ),
                                fillColor: ClassifiedAppTheme.of(context)
                                    .primaryBackground,
                                elevation: 0.0,
                                borderColor: ClassifiedAppTheme.of(context)
                                    .info
                                    .withValues(alpha: 0.5),
                                borderWidth: 1.0,
                                borderRadius: 12.0,
                                margin: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 4.0, 16.0, 4.0),
                                hidesUnderline: true,
                                isOverButton: true,
                                isSearchable: false,
                                isMultiSelect: false,
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  '3jr3k99b' /* Price */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 8.0),
                              TextFormField(
                                controller: _model.textController2,
                                focusNode: _model.textFieldFocusNode2,
                                textInputAction: TextInputAction.next,
                                obscureText: false,
                                decoration: InputDecoration(
                                  hintText: FFLocalizations.of(context).getText(
                                    'oemlegsq' /* Enter product price */,
                                  ),
                                  hintStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryText,
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w400,
                                        useGoogleFonts: false,
                                      ),
                                  filled: true,
                                  fillColor: ClassifiedAppTheme.of(context)
                                      .primaryBackground,
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: ClassifiedAppTheme.of(context)
                                          .info
                                          .withValues(alpha: 0.5),
                                      width: 1.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      width: 2.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          ClassifiedAppTheme.of(context).error,
                                      width: 1.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          ClassifiedAppTheme.of(context).error,
                                      width: 2.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  contentPadding:
                                      EdgeInsetsDirectional.fromSTEB(
                                          16.0, 16.0, 16.0, 16.0),
                                  prefixIcon: Icon(
                                    Icons.attach_money,
                                    color: ClassifiedAppTheme.of(context)
                                        .secondaryText,
                                    size: 20.0,
                                  ),
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                keyboardType: TextInputType.number,
                                cursorColor:
                                    ClassifiedAppTheme.of(context).primary,
                                validator: _model.textController3Validator
                                    .asValidator(context),
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  '96zicz46' /* Description */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 8.0),
                              TextFormField(
                                controller: _model.textController4,
                                focusNode: _model.textFieldFocusNode4,
                                textInputAction: TextInputAction.next,
                                obscureText: false,
                                decoration: InputDecoration(
                                  hintText: FFLocalizations.of(context).getText(
                                    'xgeu08dy' /* Enter product description */,
                                  ),
                                  hintStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .secondaryText,
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w400,
                                        useGoogleFonts: false,
                                      ),
                                  filled: true,
                                  fillColor: ClassifiedAppTheme.of(context)
                                      .primaryBackground,
                                  enabledBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: ClassifiedAppTheme.of(context)
                                          .info
                                          .withValues(alpha: 0.5),
                                      width: 1.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      width: 2.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  errorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          ClassifiedAppTheme.of(context).error,
                                      width: 1.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  focusedErrorBorder: OutlineInputBorder(
                                    borderSide: BorderSide(
                                      color:
                                          ClassifiedAppTheme.of(context).error,
                                      width: 2.0,
                                    ),
                                    borderRadius: BorderRadius.circular(12.0),
                                  ),
                                  contentPadding:
                                      EdgeInsetsDirectional.fromSTEB(
                                          16.0, 16.0, 16.0, 16.0),
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                maxLines: 4,
                                cursorColor:
                                    ClassifiedAppTheme.of(context).primary,
                                validator: _model.textController4Validator
                                    .asValidator(context),
                              ),
                            ],
                          ),
                        ),
                        // Location Section
                        Container(
                          margin: EdgeInsetsDirectional.fromSTEB(
                              0.0, 24.0, 0.0, 0.0),
                          padding: EdgeInsetsDirectional.fromSTEB(
                              20.0, 20.0, 20.0, 20.0),
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                              color: ClassifiedAppTheme.of(context)
                                  .info
                                  .withValues(alpha: 0.3),
                              width: 1.0,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 10.0,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(8.0),
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Icon(
                                      Icons.location_on_outlined,
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      size: 20.0,
                                    ),
                                  ),
                                  SizedBox(width: 12.0),
                                  Text(
                                    'Location Details',
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  'bxekoq73' /* Select City */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 8.0),
                              ClassifiedAppDropDown<String>(
                                controller: _model.dropDownValueController2 ??=
                                    FormFieldController<String>(null),
                                options: appStore.mainCity
                                    .map((data) => data.city.toString())
                                    .toList(),
                                onChanged: (val) {
                                  getArea(val);
                                  setState(() => _model.dropDownValue2 = val);
                                },
                                width: double.infinity,
                                height: 56.0,
                                textStyle: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                hintText: FFLocalizations.of(context).getText(
                                  'bxekoq73' /* Select City */,
                                ),
                                icon: Icon(
                                  Icons.keyboard_arrow_down_rounded,
                                  color: ClassifiedAppTheme.of(context)
                                      .secondaryText,
                                  size: 20.0,
                                ),
                                fillColor: ClassifiedAppTheme.of(context)
                                    .primaryBackground,
                                elevation: 0.0,
                                borderColor: ClassifiedAppTheme.of(context)
                                    .info
                                    .withValues(alpha: 0.5),
                                borderWidth: 1.0,
                                borderRadius: 12.0,
                                margin: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 4.0, 16.0, 4.0),
                                hidesUnderline: true,
                                isOverButton: true,
                                isSearchable: false,
                                isMultiSelect: false,
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  'unique_key_2' /* Select Area */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 8.0),
                              ClassifiedAppDropDown<String>(
                                controller: _model.dropDownValueController3 ??=
                                    FormFieldController<String>(null),
                                options: childCity
                                    .map((data) => data.city.toString())
                                    .toList(),
                                onChanged: (val) => setState(() {
                                  _model.dropDownValue3 = val;
                                  List<CityModels> selectArea = appStore.city
                                      .where((i) => i.city == val)
                                      .toList();
                                  area = selectArea[0].id;
                                }),
                                width: double.infinity,
                                height: 56.0,
                                textStyle: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w500,
                                      useGoogleFonts: false,
                                    ),
                                hintText: "Select Area",
                                icon: Icon(
                                  Icons.keyboard_arrow_down_rounded,
                                  color: ClassifiedAppTheme.of(context)
                                      .secondaryText,
                                  size: 20.0,
                                ),
                                fillColor: ClassifiedAppTheme.of(context)
                                    .primaryBackground,
                                elevation: 0.0,
                                borderColor: ClassifiedAppTheme.of(context)
                                    .info
                                    .withValues(alpha: 0.5),
                                borderWidth: 1.0,
                                borderRadius: 12.0,
                                margin: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 4.0, 16.0, 4.0),
                                hidesUnderline: true,
                                isOverButton: true,
                                isSearchable: false,
                                isMultiSelect: false,
                              ),
                            ],
                          ),
                        ),
                        // Preferences Section
                        Container(
                          margin: EdgeInsetsDirectional.fromSTEB(
                              0.0, 24.0, 0.0, 0.0),
                          padding: EdgeInsetsDirectional.fromSTEB(
                              20.0, 20.0, 20.0, 20.0),
                          decoration: BoxDecoration(
                            color: ClassifiedAppTheme.of(context)
                                .secondaryBackground,
                            borderRadius: BorderRadius.circular(16.0),
                            border: Border.all(
                              color: ClassifiedAppTheme.of(context)
                                  .info
                                  .withValues(alpha: 0.3),
                              width: 1.0,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 10.0,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: EdgeInsets.all(8.0),
                                    decoration: BoxDecoration(
                                      color: ClassifiedAppTheme.of(context)
                                          .primary
                                          .withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8.0),
                                    ),
                                    child: Icon(
                                      Icons.settings_outlined,
                                      color: ClassifiedAppTheme.of(context)
                                          .primary,
                                      size: 20.0,
                                    ),
                                  ),
                                  SizedBox(width: 12.0),
                                  Text(
                                    'Preferences',
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 18.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 20.0),
                              Text(
                                FFLocalizations.of(context).getText(
                                  'unique_key_3' /* Do you like to enable barter? */,
                                ),
                                style: ClassifiedAppTheme.of(context)
                                    .bodyMedium
                                    .override(
                                      fontFamily: 'Satoshi',
                                      fontSize: 16.0,
                                      fontWeight: FontWeight.w600,
                                      useGoogleFonts: false,
                                    ),
                              ),
                              SizedBox(height: 12.0),
                              Container(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 16.0, 16.0, 16.0),
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(12.0),
                                  border: Border.all(
                                    color: ClassifiedAppTheme.of(context)
                                        .info
                                        .withValues(alpha: 0.3),
                                    width: 1.0,
                                  ),
                                ),
                                child: ClassifiedAppRadioButton(
                                  options: ["Yes", "No"].toList(),
                                  onChanged: (val) => setState(() {
                                    barter = val;
                                  }),
                                  controller:
                                      _model.radioButtonValueController1!,
                                  optionHeight: 32.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  textPadding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 24.0, 0.0),
                                  buttonPosition: RadioButtonPosition.left,
                                  direction: Axis.horizontal,
                                  radioButtonColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  inactiveRadioButtonColor:
                                      ClassifiedAppTheme.of(context).info,
                                  toggleable: false,
                                  horizontalAlignment: WrapAlignment.start,
                                  verticalAlignment: WrapCrossAlignment.start,
                                ),
                              ),
                              SizedBox(height: 20.0),
                              Row(
                                children: [
                                  Icon(
                                    Icons.phone_outlined,
                                    color:
                                        ClassifiedAppTheme.of(context).primary,
                                    size: 20.0,
                                  ),
                                  SizedBox(width: 8.0),
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      'unique_key_4' /* enable call? */,
                                    ),
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 16.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                  if (appStore.user_mobile != null)
                                    Container(
                                      margin: EdgeInsetsDirectional.fromSTEB(
                                          8.0, 0.0, 0.0, 0.0),
                                      padding: EdgeInsetsDirectional.fromSTEB(
                                          8.0, 4.0, 8.0, 4.0),
                                      decoration: BoxDecoration(
                                        color: ClassifiedAppTheme.of(context)
                                            .primary
                                            .withValues(alpha: 0.1),
                                        borderRadius:
                                            BorderRadius.circular(6.0),
                                      ),
                                      child: Text(
                                        '(+971 ${appStore.user_mobile})',
                                        style: ClassifiedAppTheme.of(context)
                                            .bodySmall
                                            .override(
                                              fontFamily: 'Satoshi',
                                              fontSize: 12.0,
                                              color:
                                                  ClassifiedAppTheme.of(context)
                                                      .primary,
                                              fontWeight: FontWeight.w600,
                                              useGoogleFonts: false,
                                            ),
                                      ),
                                    ),
                                ],
                              ),
                              SizedBox(height: 12.0),
                              Container(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 16.0, 16.0, 16.0),
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(12.0),
                                  border: Border.all(
                                    color: ClassifiedAppTheme.of(context)
                                        .info
                                        .withValues(alpha: 0.3),
                                    width: 1.0,
                                  ),
                                ),
                                child: ClassifiedAppRadioButton(
                                  options: ["Yes", "No"].toList(),
                                  onChanged: (val) => setState(() {
                                    // Handle call preference
                                  }),
                                  controller:
                                      _model.radioButtonValueController2!,
                                  optionHeight: 32.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  textPadding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 24.0, 0.0),
                                  buttonPosition: RadioButtonPosition.left,
                                  direction: Axis.horizontal,
                                  radioButtonColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  inactiveRadioButtonColor:
                                      ClassifiedAppTheme.of(context).info,
                                  toggleable: false,
                                  horizontalAlignment: WrapAlignment.start,
                                  verticalAlignment: WrapCrossAlignment.start,
                                ),
                              ),
                              SizedBox(height: 20.0),
                              Row(
                                children: [
                                  Icon(
                                    Icons.chat_outlined,
                                    color:
                                        ClassifiedAppTheme.of(context).primary,
                                    size: 20.0,
                                  ),
                                  SizedBox(width: 8.0),
                                  Text(
                                    FFLocalizations.of(context).getText(
                                      'unique_key_5' /* enable chat? */,
                                    ),
                                    style: ClassifiedAppTheme.of(context)
                                        .bodyMedium
                                        .override(
                                          fontFamily: 'Satoshi',
                                          fontSize: 16.0,
                                          fontWeight: FontWeight.w600,
                                          useGoogleFonts: false,
                                        ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 12.0),
                              Container(
                                padding: EdgeInsetsDirectional.fromSTEB(
                                    16.0, 16.0, 16.0, 16.0),
                                decoration: BoxDecoration(
                                  color: ClassifiedAppTheme.of(context)
                                      .primaryBackground,
                                  borderRadius: BorderRadius.circular(12.0),
                                  border: Border.all(
                                    color: ClassifiedAppTheme.of(context)
                                        .info
                                        .withValues(alpha: 0.3),
                                    width: 1.0,
                                  ),
                                ),
                                child: ClassifiedAppRadioButton(
                                  options: ["Yes", "No"].toList(),
                                  onChanged: (val) => setState(() {
                                    // Handle chat preference
                                  }),
                                  controller:
                                      _model.radioButtonValueController3!,
                                  optionHeight: 32.0,
                                  textStyle: ClassifiedAppTheme.of(context)
                                      .labelMedium
                                      .override(
                                        fontFamily: 'Satoshi',
                                        color: ClassifiedAppTheme.of(context)
                                            .primaryText,
                                        fontSize: 16.0,
                                        fontWeight: FontWeight.w500,
                                        useGoogleFonts: false,
                                      ),
                                  textPadding: EdgeInsetsDirectional.fromSTEB(
                                      0.0, 0.0, 24.0, 0.0),
                                  buttonPosition: RadioButtonPosition.left,
                                  direction: Axis.horizontal,
                                  radioButtonColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  inactiveRadioButtonColor:
                                      ClassifiedAppTheme.of(context).info,
                                  toggleable: false,
                                  horizontalAlignment: WrapAlignment.start,
                                  verticalAlignment: WrapCrossAlignment.start,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 30,
                        ),
                        // Enhanced Submit Button
                        Container(
                          margin: EdgeInsetsDirectional.fromSTEB(
                              0.0, 24.0, 0.0, 24.0),
                          child: Opacity(
                            opacity: (nb_utils.isWeb
                                    ? MediaQuery.viewInsetsOf(context).bottom >
                                        0
                                    : _isKeyboardVisible)
                                ? 0.0
                                : 1.0,
                            child: Container(
                              width: double.infinity,
                              height: 60.0,
                              child: ElevatedButton(
                                onPressed: () async {
                                  // if (_model.formKey.currentState == null ||
                                  //     !_model.formKey.currentState!.validate()) {
                                  //   return;
                                  // }

                                  // if (_model.dropDownValue1 == null) {
                                  //   return;
                                  // }
                                  // if (_model.dropDownValue2 == null) {
                                  //   return;
                                  // }
                                  // if (_model.dropDownValue3 == null) {
                                  //   return;
                                  // }
                                  // if (_model.dropDownValue4 == null) {
                                  //   return;
                                  // }
                                  // if (_model.dropDownValue5 == null) {
                                  //   return;
                                  // }
                                  if (appStore.user_mobile == null) {
                                    toster("Please Update Your Profile");
                                    return;
                                  }

                                  if (_model.formKey.currentState == null ||
                                      !_model.formKey.currentState!
                                          .validate()) {
                                    nb_utils.toast("Please fill all the fields",
                                        bgColor: Colors.red,
                                        textColor: Colors.black);
                                    return;
                                  }

                                  if (_model.dropDownValue1 == null) {
                                    return;
                                  }
                                  if (_model.dropDownValue2 == null) {
                                    return;
                                  }
                                  if (_model.dropDownValue3 == null) {
                                    return;
                                  }
                                  if (_model.dropDownValue4 == null) {
                                    return;
                                  }

                                  if (FFAppState().selectedCategoryId == 0) {
                                    toster("Please Choose Category");
                                    return;
                                  }
                                  if (!FFAppState().haveChilsCat) {
                                    toster("Please Choose Sub Category");
                                    return;
                                  }
                                  if (FFAppState().haveSecondCat) {
                                    if (FFAppState()
                                            .selectedSecondChildCategoryId ==
                                        null) {
                                      toster("Please Choose Child Category");
                                      return;
                                    }
                                  }

                                  submitPost();
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      ClassifiedAppTheme.of(context).primary,
                                  foregroundColor: Colors.white,
                                  elevation: 0.0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16.0),
                                  ),
                                  padding: EdgeInsetsDirectional.fromSTEB(
                                      24.0, 0.0, 24.0, 0.0),
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.all(8.0),
                                      decoration: BoxDecoration(
                                        color:
                                            Colors.white.withValues(alpha: 0.2),
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                      ),
                                      child: Icon(
                                        Icons.add_circle_outline,
                                        color: Colors.white,
                                        size: 24.0,
                                      ),
                                    ),
                                    SizedBox(width: 12.0),
                                    Text(
                                      'Add Post',
                                      style: ClassifiedAppTheme.of(context)
                                          .titleMedium
                                          .override(
                                            fontFamily: 'Satoshi',
                                            color: Colors.white,
                                            fontSize: 18.0,
                                            fontWeight: FontWeight.bold,
                                            useGoogleFonts: false,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          )
        : Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                'assets/images/access-denied.png',
                width: 120.0,
                height: 120.0,
                fit: BoxFit.contain,
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 28.0, 0.0, 0.0),
                child: Text(
                  "Invalid Access",
                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                        fontFamily: 'Satoshi',
                        fontSize: 24.0,
                        fontWeight: FontWeight.bold,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(0.0, 16.0, 0.0, 0.0),
                child: Text(
                  "Please Login Create Your Post!",
                  textAlign: TextAlign.center,
                  style: ClassifiedAppTheme.of(context).bodyMedium.override(
                        fontFamily: 'Satoshi',
                        fontSize: 17.0,
                        fontWeight: FontWeight.w500,
                        useGoogleFonts: false,
                      ),
                ),
              ),
              Padding(
                padding: EdgeInsetsDirectional.fromSTEB(54.0, 28.0, 54.0, 0.0),
                child: wrapWithModel(
                  model: _model.appButtonModel,
                  updateCallback: () => setState(() {}),
                  child: AppButtonWidget(
                    text: 'Login Now',
                    action: () async {
                      context.goNamed(
                        'LoginPage',
                        extra: <String, dynamic>{
                          kTransitionInfoKey: TransitionInfo(
                            hasTransition: true,
                            transitionType: PageTransitionType.rightToLeft,
                            duration: Duration(milliseconds: 300),
                          ),
                        },
                      );
                    },
                  ),
                ),
              ),
            ],
          );
  }

  void toster(message) {
    nb_utils.toast("${message}", bgColor: Colors.red, textColor: Colors.black);
  }
}
